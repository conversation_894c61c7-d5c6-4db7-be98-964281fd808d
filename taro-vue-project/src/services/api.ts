// API服务接口 - 从WePY2的services.js迁移到Taro
import { post } from './request'

// ==================== 首页相关接口 ====================

/**
 * 首页轮播
 * @returns Promise
 */
export function homeBanner() {
  return post('/HomepageSlide')
}

/**
 * 首页栏目列表
 * @returns Promise
 */
export function menuList() {
  return post('/HomepageMenu')
}

/**
 * 首页分类列表
 * @returns Promise
 */
export function homeCategoryList() {
  return post('/homepageCategory')
}

// ==================== 分类相关接口 ====================

/**
 * 所有分类
 * @returns Promise
 */
export function categoryAllList() {
  return post('/Category')
}

/**
 * 一级分类
 * @returns Promise
 */
export function categoryList() {
  return post('/CategoryList')
}

/**
 * 二级分类
 * @param data 分类参数
 * @returns Promise
 */
export function category2List(data: any) {
  return post('/CategoryLevel1', { data })
}

/**
 * 三级分类
 * @param data 分类参数
 * @returns Promise
 */
export function category3List(data: any) {
  return post('/CategoryLevel2', { data })
}

// ==================== 商品相关接口 ====================

/**
 * 产品列表
 * @param data 查询参数
 * @returns Promise
 */
export function getProductList(data: any) {
  return post('/Product', { data })
}

/**
 * 获取商品详情
 * @param data 商品参数
 * @returns Promise
 */
export function getProDetail(data: any) {
  return post('/ProductDetails', { data })
}

/**
 * 商品详情轮播图
 * @param data 商品参数
 * @returns Promise
 */
export function getProBanner(data: any) {
  return post('/ProductRotateImage', { data })
}

/**
 * 产品数量购买价格
 * @param data 商品参数
 * @returns Promise
 */
export function getProQtyPurchase(data: any) {
  return post('/ProductQtyPurchase', { data })
}

/**
 * 产品规格选项
 * @param data 商品参数
 * @returns Promise
 */
export function getProductOption(data: any) {
  return post('/ProductOption', { data })
}

/**
 * 国家产品规格选项
 * @param data 商品参数
 * @returns Promise
 */
export function ProductOptionCountry(data: any) {
  return post('/ProductOptionCountry', { data })
}

/**
 * 规格选项组合
 * @param data 规格参数
 * @returns Promise
 */
export function ProductSpecCombination(data: any) {
  return post('/ProductSpecCombination', { data })
}

/**
 * 产品规格价格
 * @param data 规格参数
 * @returns Promise
 */
export function ProductSKU(data: any) {
  return post('/ProductSKU', { data })
}

// ==================== 购物车相关接口 ====================

/**
 * 加入购物车
 * @param data 购物车参数
 * @returns Promise
 */
export function AddToCart(data: any) {
  return post('/AddToCart', { data })
}

/**
 * 更新购物车
 * @param data 购物车参数
 * @returns Promise
 */
export function UpdateToCart(data: any) {
  return post('/UpdateToCart', { data })
}

/**
 * 购物车列表
 * @param data 查询参数
 * @returns Promise
 */
export function GetShoppingCart(data: any) {
  return post('/GetShoppingCart', { data })
}

// ==================== 订单相关接口 ====================

/**
 * 提交订单
 * @param data 订单参数
 * @returns Promise
 */
export function Checkout(data: any) {
  return post('/Checkout', { data })
}

/**
 * 支付
 * @param data 支付参数
 * @returns Promise
 */
export function SaveCart(data: any) {
  return post('/SaveCart', { data })
}

/**
 * 待支付里继续支付
 * @param data 支付参数
 * @returns Promise
 */
export function CompletePayment(data: any) {
  return post('/CompletePayment', { data })
}

/**
 * 订单列表
 * @param data 查询参数
 * @returns Promise
 */
export function DispurList(data: any) {
  return post('/DispurList', { data })
}

/**
 * 订单详情
 * @param data 订单参数
 * @returns Promise
 */
export function DispurDetail(data: any) {
  return post('/DispurDetail', { data })
}

/**
 * 查询快递单号
 * @param data 快递参数
 * @returns Promise
 */
export function CheckTrackingNo(data: any) {
  return post('/CheckTrackingNo', { data })
}

/**
 * 确认收货
 * @param data 订单参数
 * @returns Promise
 */
export function UpdateReceive(data: any) {
  return post('/UpdateReceive', { data })
}

/**
 * 取消待付款订单
 * @param data 订单参数
 * @returns Promise
 */
export function CancelPendingPaymentSales(data: any) {
  return post('/CancelPendingPaymentSales', { data })
}

// ==================== 售后相关接口 ====================

/**
 * 退货订单
 * @param data 查询参数
 * @returns Promise
 */
export function DispurListForStockReturn(data: any) {
  return post('/DispurListForStockReturn', { data })
}

/**
 * 退货订单详细
 * @param data 订单参数
 * @returns Promise
 */
export function DispurDetailForStockReturn(data: any) {
  return post('/DispurDetailForStockReturn', { data })
}

/**
 * 退货原因列表
 * @param data 查询参数
 * @returns Promise
 */
export function GetReason(data: any) {
  return post('/GetReason', { data })
}

/**
 * 物流公司编码列表
 * @param data 查询参数
 * @returns Promise
 */
export function GetCourierList(data: any) {
  return post('/GetCourierList', { data })
}

/**
 * 确认收货换货商品
 * @param data 订单参数
 * @returns Promise
 */
export function UpdateReceiveReturn(data: any) {
  return post('/UpdateReceiveReturn', { data })
}

/**
 * 用户退货填写追踪编号和公司编码
 * @param data 退货参数
 * @returns Promise
 */
export function UpdateStockReturnStatus(data: any) {
  return post('/UpdateStockReturnStatus', { data })
}

/**
 * 退货退款数额
 * @param data 退货参数
 * @returns Promise
 */
export function StockReturnRefundAmt(data: any) {
  return post('/StockReturnRefundAmt', { data })
}

/**
 * 确认退货
 * @param data 退货参数
 * @returns Promise
 */
export function SaveReturnRefund(data: any) {
  return post('/SaveReturnRefund', { data })
}

/**
 * 提醒发货
 * @param data 订单参数
 * @returns Promise
 */
export function DeliveryNotice(data: any) {
  return post('/DeliveryNotice', { data })
}

// ==================== 文件上传相关接口 ====================

/**
 * 七牛Token
 * @param data 上传参数
 * @returns Promise
 */
export function QiniuToken(data: any) {
  return post('/QiniuToken', { data })
}

// ==================== 钱包相关接口 ====================

/**
 * 提现数据
 * @param data 查询参数
 * @returns Promise
 */
export function WithdrawalInfo(data: any) {
  return post('/WithdrawalInfo', { data })
}

/**
 * 提现
 * @param data 提现参数
 * @returns Promise
 */
export function Withdrawal(data: any) {
  return post('/Withdrawal', { data })
}

/**
 * 钱包余额
 * @param data 查询参数
 * @returns Promise
 */
export function WalletBalance(data: any) {
  return post('/WalletBalance', { data })
}

/**
 * 钱包明细
 * @param data 查询参数
 * @returns Promise
 */
export function WalletDetail(data: any) {
  return post('/WalletDetail', { data })
}

// ==================== 用户相关接口 ====================

/**
 * 用户登录 - 从WePY项目迁移
 * @param data 登录参数
 * @returns Promise
 */
export function fetchLogin(data: any) {
  return post('/Login', { data })
}

/**
 * 获取用户信息 - 从WePY项目迁移
 * @param data 查询参数
 * @returns Promise
 */
export function getUserInfo(data?: any) {
  return post('/GetUserInfo', { data })
}

/**
 * 获取手机号 - 从WePY项目迁移
 * @param data 手机号参数
 * @returns Promise
 */
export function GetPhoneNo(data: any) {
  return post('/GetPhoneNo', { data })
}

/**
 * 获取验证码 - 从WePY项目迁移
 * @param data 验证码参数
 * @returns Promise
 */
export function getCode(data: any) {
  return post('/RequestVerificationCode', { data })
}

/**
 * 微信注册会员 - 从WePY项目迁移
 * @param data 注册参数
 * @returns Promise
 */
export function WechatRegisterMember(data: any) {
  return post('/WechatRegisterMember', { data })
}

/**
 * 钱包充值 - 从WePY项目迁移
 * @param data 充值参数
 * @returns Promise
 */
export function SFWalletTopUpSC(data: any) {
  return post('/SFWalletTopUpSC', { data })
}

/**
 * 用户登录
 * @param data 登录参数
 * @returns Promise
 */
export function Login(data: any) {
  return post('/Login', { data })
}

/**
 * 用户注册
 * @param data 注册参数
 * @returns Promise
 */
export function Register(data: any) {
  return post('/Register', { data })
}

/**
 * 获取用户信息
 * @param data 查询参数
 * @returns Promise
 */
export function GetUserInfo(data: any) {
  return post('/GetUserInfo', { data })
}

/**
 * 更新用户信息
 * @param data 用户信息
 * @returns Promise
 */
export function UpdateUserInfo(data: any) {
  return post('/UpdateUserInfo', { data })
}

/**
 * 修改密码
 * @param data 密码参数
 * @returns Promise
 */
export function ChangePassword(data: any) {
  return post('/ChangePassword', { data })
}

/**
 * 重置密码
 * @param data 重置参数
 * @returns Promise
 */
export function ResetPassword(data: any) {
  return post('/ResetPassword', { data })
}

// ==================== 地址相关接口 ====================

/**
 * 获取地址列表
 * @param data 查询参数
 * @returns Promise
 */
export function GetAddressList(data: any) {
  return post('/GetAddressList', { data })
}

/**
 * 添加地址
 * @param data 地址信息
 * @returns Promise
 */
export function AddAddress(data: any) {
  return post('/AddAddress', { data })
}

/**
 * 更新地址
 * @param data 地址信息
 * @returns Promise
 */
export function UpdateAddress(data: any) {
  return post('/UpdateAddress', { data })
}

/**
 * 删除地址
 * @param data 地址参数
 * @returns Promise
 */
export function DeleteAddress(data: any) {
  return post('/DeleteAddress', { data })
}

/**
 * 设置默认地址
 * @param data 地址参数
 * @returns Promise
 */
export function SetDefaultAddress(data: any) {
  return post('/SetDefaultAddress', { data })
}

// ==================== 搜索相关接口 ====================

/**
 * 搜索商品
 * @param data 搜索参数
 * @returns Promise
 */
export function SearchProduct(data: any) {
  return post('/SearchProduct', { data })
}

/**
 * 搜索历史
 * @param data 查询参数
 * @returns Promise
 */
export function SearchHistory(data: any) {
  return post('/SearchHistory', { data })
}

/**
 * 热门搜索
 * @param data 查询参数
 * @returns Promise
 */
export function HotSearch(data: any) {
  return post('/HotSearch', { data })
}

// ==================== 首页专用接口 - 从WePY项目迁移 ====================

/**
 * 新闻公告列表
 * @param data 查询参数
 * @returns Promise
 */
export function NewsListing(data: any) {
  return post('/NewsListing', { data })
}

/**
 * 限时抢购商品
 * @param data 查询参数
 * @returns Promise
 */
export function LimitedTimeProduct(data: any) {
  return post('/LimitedTimeProduct', { data })
}

/**
 * 拼团商品
 * @param data 查询参数
 * @returns Promise
 */
export function GroupPurchaseProduct(data: any) {
  return post('/GroupPurchaseProduct', { data })
}

/**
 * 预热限时抢购商品
 * @param data 查询参数
 * @returns Promise
 */
export function PreLimitedTimeProduct(data: any) {
  return post('/PreLimitedTimeProduct', { data })
}

/**
 * 预热拼团商品
 * @param data 查询参数
 * @returns Promise
 */
export function PreGroupPurchaseProduct(data: any) {
  return post('/PreGroupPurchaseProduct', { data })
}

/**
 * 首页国家品牌分类
 * @returns Promise
 */
export function HomePageCountryBrand() {
  return post('/HomePageCountryBrand')
}

/**
 * 服务首页主分类
 * @returns Promise
 */
export function ServiceHomePageMainCategory() {
  return post('/ServiceHomePageMainCategory')
}

/**
 * 红包详情列表
 * @param data 查询参数
 * @returns Promise
 */
export function Listing_AngpaoBonusDetail(data: any) {
  return post('/Listing_AngpaoBonusDetail', { data })
}

/**
 * 检查红包
 * @param data 红包参数
 * @returns Promise
 */
export function CheckAngPao(data: any) {
  return post('/CheckAngPao', { data })
}

/**
 * 检查登录状态
 * @returns Promise
 */
export function CheckLogin() {
  return post('/CheckLogin')
}

/**
 * 更新会员店铺名称
 * @param data 店铺信息
 * @returns Promise
 */
export function UpdateMemberShopName(data: any) {
  return post('/UpdateMemberShopName', { data })
}

/**
 * 爆品推荐
 * @param data 查询参数
 * @returns Promise
 */
export function FeaturedProduct(data: any) {
  return post('/FeaturedProduct', { data })
}

// ==================== 地区相关接口 - 从WePY项目迁移 ====================

/**
 * 获取省份列表
 * @param data 查询参数
 * @returns Promise
 */
export function getState(data: any) {
  return post('/GetState', { data })
}

/**
 * 获取城市列表
 * @param data 查询参数
 * @returns Promise
 */
export function getCity(data: any) {
  return post('/GetCity', { data })
}

/**
 * 获取区县列表
 * @param data 查询参数
 * @returns Promise
 */
export function getDistrict(data: any) {
  return post('/GetDistrict', { data })
}
