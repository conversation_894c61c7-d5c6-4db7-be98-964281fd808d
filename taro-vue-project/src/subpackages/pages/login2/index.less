/* 登录页面样式 - 从WePY2迁移到Taro + Vue3 */
@import "../../../common/common.less";

.bar {
  background-color: @primary-color;
  position: sticky;
  top: 0;
  z-index: 999;
}

.header {
  .con {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 38rpx;

    > .home {
      position: absolute;
      left: 49rpx;
      top: 40%;

      image {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .actions {
      position: absolute;
      left: 49rpx;
      top: 50%;
      margin-top: -29rpx;
      width: 165rpx;
      height: 58rpx;
      border: 2rpx solid @primary-color;
      border-radius: 29rpx;
      display: flex;
      overflow: hidden;

      view {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .back {
          font-size: 40rpx;
          color: @primary-color;
        }

        .home {
          width: 44rpx;
          height: 44rpx;
        }
      }

      .left::before {
        content: "";
        position: absolute;
        right: 0;
        top: 11rpx;
        width: 2rpx;
        height: 37rpx;
        background: @primary-color;
      }
    }
  }
}

.bg {
  display: block;
  width: 100%;
  height: 550rpx;
  margin: auto;
}

.tabs {
  .flex(center, center);
  font-size: 54rpx;
  color: #929292;
  margin-top: 20rpx;
  transition: all 0.3s ease-in;

  .en {
    font-size: 30rpx;
  }

  .col1 {
    padding-right: 38rpx;
    border-right: 4rpx solid #e0e0e0;
  }

  .col2 {
    padding-left: 38rpx;
  }

  .active {
    color: #010101;
  }
}

.box {
  margin: 85rpx auto 0;
  border: 2rpx solid #c6c6c6;
  width: 540rpx;

  .row {
    height: 134rpx;
    box-sizing: border-box;
    padding: 28rpx 40rpx;

    .key {
      font-size: 28rpx;
      color: #131313;
    }

    .val {
      margin-top: 10rpx;
      .nut-input {
        padding: 0;
        background-color: transparent;
      }
      input {
        font-size: 30rpx;
        color: #131313;
      }
    }
  }

  .row1 {
    border-bottom: 1rpx solid #c6c6c6;
  }

  .row2 {
    padding-top: 23rpx;

    .val {
      .flex();
      margin-top: 0;

      input {
        flex: 1;
      }

      .code {
        flex-shrink: 0;
        .flex(center, center);
        padding: 0 10rpx;
        height: 57rpx;
        background: #158787;
        border-radius: 5rpx;
        color: #fff;
        font-size: 28rpx;
      }

      .disabled {
        background-color: #f5f5f5;
        color: #00000040;
        border: 2rpx solid #d9d9d9;
        padding: 0 10rpx;
      }
    }
  }

  .row3 {
    border-top: 1rpx solid #c6c6c6;
    view {
      font-size: 30rpx;
      color: gray;
    }
  }
}

.loginType {
  width: 540rpx;
  font-size: 28rpx;
  color: #158787;
  text-align: right;
  margin: 20rpx auto 0;
}

.other {
  margin: 50rpx auto 0;
  width: 540rpx;
  .flex();

  .col1 {
    font-size: 28rpx;
    color: #158787;
    font-weight: 500;
    background-color: transparent;

    &::after {
      border: none;
    }
  }

  .col2 {
    font-size: 28rpx;
    font-weight: 500;
    color: #6f6f6f;

    label {
      color: #158787;
    }
  }
}

.wx-register {
  font-size: 28rpx;
  color: #549795;
  font-weight: 500;
  margin-top: 43rpx;
  text-align: center;
}

.recharge {
  .header {
    position: relative;
    display: flex;
    justify-content: center;
    padding: 30rpx 20rpx;

    .close {
      position: absolute;
      right: 20rpx;
      top: 30rpx;
      font-size: 41rpx;
      color: #8cdac6;
    }

    .title {
      font-size: 40rpx;
      text-align: center;
      font-weight: 500;
    }
  }

  input {
    margin: 20rpx 40rpx 0;
    height: 94rpx;
    background-color: #f4f4f4;
    border-radius: 10rpx;
    font-size: 43rpx;
    font-weight: 500;
    text-align: center;
  }

  .protocol {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 80rpx;

    .left {
      color: #b9b9b9;
    }

    .right {
      color: #1f1f1f;
    }
  }

  .btn {
    width: 239rpx;
    line-height: 62rpx;
    text-align: center;
    background: #08c05f;
    border-radius: 10rpx;
    font-size: 30rpx;
    color: #fff;
    margin: 54rpx auto 0;
  }
}

.btns {
  .flex(center, center);
  margin-top: 60rpx;
  padding-bottom: 50rpx;

  button {
    position: relative;
    width: 205rpx;
    line-height: 56rpx;
    text-align: center;
    color: #fff;
    background-color: transparent;
    padding: 0;

    &::after {
      border: none;
    }

    image {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    text {
      position: relative;
      color: #fff;
      z-index: 9;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.links {
  display: flex;
  align-items: center;
  justify-content: center;

  label {
    margin-right: 20rpx;
    color: @primary-color;
  }
}

/* 按钮背景样式 */
.btn3-bg {
  background: linear-gradient(135deg, @primary-color 0%, @secondary-color 100%);
  color: #0e5f5f;
  border: none;
  border-radius: 30rpx;
  font-weight: bold;
}
